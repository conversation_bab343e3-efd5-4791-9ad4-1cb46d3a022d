import json
from typing import Union, Dict, Optional

# 默认敏感词库
DEFAULT_SENSITIVE_WORDS = {
    "支付宝": "**",
    "微信支付": "****",
    "财付通": "***",
    "京东": "**",
    "抖店": "**",
    "手机号": "**",
    "微信": "**",
    "联系方式": "",
    "QQ": "**",
    "微信": "**",
    "抖音": "**",
    "快手": "**",
    "唯品会": "**"
}

def parse_filters(filters_text: Optional[str] = None) -> Dict[str, str]:
    """
    解析过滤规则文本，转换为替换规则字典
    :param filters_text: 包含替换规则的文本，如果为None则使用默认敏感词库
    :return: 替换规则字典
    """
    if not filters_text:
        return DEFAULT_SENSITIVE_WORDS
        
    replacements = {}
    try:
        # 按行分割规则
        rules = filters_text.strip().split('\n')
        for rule in rules:
            if '=' in rule:
                key, value = rule.split('=', 1)
                # 去除引号
                value = value.strip('"')
                replacements[key] = value
        return replacements
    except Exception as e:
        print(f"解析过滤规则失败: {str(e)}")
        return DEFAULT_SENSITIVE_WORDS

def filter_sensitive_words(text: str, replacements: Dict[str, str]) -> str:
    """
    应用敏感词过滤规则，替换或删除指定内容
    :param text: 输入文本
    :param replacements: 替换规则字典
    :return: 处理后的文本
    """
    result = text
    for key, value in replacements.items():
        if value:  # 如果有替换值
            result = result.replace(key, value)
        else:  # 如果替换值为空，则删除该内容
            result = result.replace(key, '')
    return result

def process_input(data: dict) -> dict:
    """
    处理输入数据
    :param data: 输入的字典数据
    :return: 处理结果
    """
    try:
        content = data.get('content', '')
        
        # 如果没有content，返回错误
        if not content:
            return {'result': "输入数据格式错误，缺少content字段"}
            
        # 获取filters，如果为None或空则使用默认敏感词库
        filters = data.get('filters')
        
        # 解析过滤规则
        replacements = parse_filters(filters)
            
        # 应用过滤规则
        filtered_text = filter_sensitive_words(content, replacements)
        return {'result': filtered_text}
        
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

def main(input_data: Union[str, dict] = None, **kwargs) -> dict:
    """
    主函数，处理输入并返回敏感词过滤结果
    :param input_data: JSON格式的字符串或字典数据
    :param kwargs: 其他关键字参数（支持直接传入content和filters）
    :return: 包含处理结果的字典
    """
    try:
        # 如果通过kwargs提供了参数
        if 'content' in kwargs:
            data = {
                'content': kwargs['content']
            }
            # 如果提供了filters，添加到数据中
            if 'filters' in kwargs:
                data['filters'] = kwargs['filters']
            return process_input(data)
            
        # 如果提供了input_data
        if input_data:
            # 如果是字典类型，直接使用
            if isinstance(input_data, dict):
                return process_input(input_data)
            # 如果是字符串，尝试解析JSON
            elif isinstance(input_data, str):
                data = json.loads(input_data)
                return process_input(data)
                
        # 如果没有提供任何输入，使用默认测试数据
        default_input = '''{
          "content": "不便提供微信号，建议通过官方渠道联系获取帮助。",
          "filters": null
        }'''
        data = json.loads(default_input)
        return process_input(data)

    except json.JSONDecodeError:
        return {'result': "输入不是有效的JSON格式"}
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 程序入口点
if __name__ == "__main__":
    # 测试输入 - 完整数据
    test_input1 = {
        "content": "不便提供微信号，建议通过官方渠道联系获取帮助。",
        "filters": None
    }
    
    test_input2 = {
        "content": "请通过支付宝或微信支付付款，有问题可以联系微信号：abc123，或者QQ：456789",
        "filters": None
    }
    
    test_input3 = {
        "content": "这款永劫无间游戏真好玩，可以在唯品会购买。",
        "filters": None
    }
    
    # 测试不同的输入
    result1 = main(test_input1)
    result2 = main(test_input2)  
    result3 = main(test_input3)
    
    # 打印结果
    print("测试1结果:", result1['result'])
    print("测试2结果:", result2['result'])
    print("测试3结果:", result3['result'])

    # 预期输出结果示例：
    # 测试1结果: 不便提供，建议通过官方渠道联系获取帮助。
    # 测试2结果: 请通过这个或这个付款，有问题可以联系：abc123，或者：456789
    # 测试3结果: 这款永J无间游戏真好玩，可以在这个购买。 