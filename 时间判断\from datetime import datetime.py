from datetime import datetime
import json
import sys

def check_work_time(time_str):
    # 解析时间字符串
    dt = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S")
    
    # 获取小时
    hour = dt.hour
    
    # 判断是否在上班时间范围内
    if 9 <= hour < 22:
        return "上班时间"
    else:
        return "休息时间"

def main(text=None):  # 添加参数接收输入的时间
    try:
        if text:  # 如果直接传入时间字符串
            result = check_work_time(text)
        else:  # 否则从标准输入读取
            input_data = json.loads(sys.stdin.read())
            # 尝试获取 time_str 或 text 字段
            text = input_data.get("time_str") or input_data.get("text", "")
            result = check_work_time(text)
        print(result)
    except json.JSONDecodeError:
        default_time = "2025-04-18 01:13:59"
        print(f"无效的JSON输入，使用默认时间：{default_time}")
        result = check_work_time(default_time)
        print(result)

if __name__ == "__main__":
    main()  # 不带参数调用，将从标准输入读取