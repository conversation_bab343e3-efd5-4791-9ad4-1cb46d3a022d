def parse_input_string(input_str: str) -> dict:
    """
    从字符串输入中提取身高和体重数据
    :param input_str: 输入字符串，支持多种格式
    :return: 包含提取结果的字典 {height_cm, weight_jin}
    """
    try:
        import re
        
        # 清理输入字符串
        input_str = input_str.lower().strip()
        
        # 存储解析结果
        result = {}
        
        # 提取身高
        height_match = re.search(r'身高\s*[：:]?\s*(\d+(?:\.\d+)?)', input_str)
        if height_match:
            result['height_cm'] = float(height_match.group(1))
        else:
            # 尝试找到带有cm的数字
            height_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(?:cm|厘米)', input_str)
            if height_matches:
                result['height_cm'] = float(height_matches[0])
        
        # 提取体重
        weight_match = re.search(r'体重\s*[：:]?\s*(\d+(?:\.\d+)?)', input_str)
        if weight_match:
            result['weight_jin'] = float(weight_match.group(1))
        else:
            # 尝试找到带有斤的数字
            weight_matches = re.findall(r'(\d+(?:\.\d+)?)\s*(?:斤|jin)', input_str)
            if weight_matches:
                result['weight_jin'] = float(weight_matches[0])
            else:
                raise ValueError("无法找到体重数据")
        
        # 至少需要体重信息
        if 'weight_jin' not in result:
            raise ValueError("无法找到体重数据")
            
        return result
        
    except Exception as e:
        raise ValueError(f"解析输入失败: {str(e)}")

def parse_size_table(size_data_list: list) -> list:
    """
    从DIFY输入格式中解析尺码表数据
    :param size_data_list: DIFY格式的尺码表数据列表
    :return: 解析后的尺码表列表
    """
    if not size_data_list or not isinstance(size_data_list, list):
        raise ValueError("尺码表数据无效")
        
    try:
        # 合并所有尺码表内容
        all_content = ""
        for data in size_data_list:
            content = data.get('content', '')
            if content:
                all_content += content + "\n"
                
        if not all_content:
            raise ValueError("尺码表内容为空")
            
        # 解析尺码表数据
        import re
        
        # 尝试所有可能的格式
        patterns = [
            # 格式1: 体重范围：110-120斤，推荐S码（29）
            r'体重范围：(\d+)-(\d+)斤，推荐([A-Z0-9]+)码[（(](\d+)[）)]',
            # 格式2: 体重110-120斤的推荐S码（29）
            r'体重(\d+)-(\d+)斤的推荐([A-Z0-9]+)码[（(](\d+)[码]?[）)]',
            # 格式3: 体重110-130斤的推荐M码（48码）
            r'体重(\d+)-(\d+)斤的推荐([A-Z0-9]+)码[（(](\d+)码[）)]',
            # 格式4: 体重范围：96-105斤，推荐26码（S码）- 字母尺码可选
            r'体重范围：(\d+)-(\d+)斤，推荐(\d+)码(?:[（(]([A-Z]+)码[）)])?',
            # 格式5: 体重范围：80-95斤，推荐XS码（25码）- 字母尺码在前，数字尺码在括号
            r'体重范围：(\d+)-(\d+)斤，推荐([A-Z]+)码[（(](\d+)码?[）)]'
        ]
        
        matches = []
        used_pattern_index = -1
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, all_content)
            if matches:
                used_pattern_index = i
                break
            
        if not matches:
            raise ValueError("无法从尺码表中提取有效数据")
            
        # 转换为结构化数据
        size_table = []
        
        # 如果是格式4（最后一个模式），处理方式略有不同
        if used_pattern_index == 3:  # 第4个模式（索引为3）
            for match in matches:
                weight_min = float(match[0])
                weight_max = float(match[1])
                number_size = match[2]  # 这里是数字尺码
                letter_size = match[3] if match[3] else number_size  # 如果没有字母尺码，使用数字尺码
                
                size_table.append({
                    'weight_min': weight_min,
                    'weight_max': weight_max,
                    'letter_size': letter_size,
                    'number_size': int(number_size)
                })
        else:
            for match in matches:
                weight_min = float(match[0])
                weight_max = float(match[1])
                letter_size = match[2]
                number_size = int(match[3])
                
                size_table.append({
                    'weight_min': weight_min,
                    'weight_max': weight_max,
                    'letter_size': letter_size,
                    'number_size': number_size
                })
            
        return sorted(size_table, key=lambda x: x['weight_min'])
        
    except Exception as e:
        raise ValueError(f"解析尺码表数据时出错: {str(e)}")

def recommend_size(weight_jin: float, size_table: list, height_cm: float = None) -> dict:
    """
    根据体重(和身高)推荐尺码
    :param weight_jin: 体重(斤)
    :param size_table: 尺码表
    :param height_cm: 身高(厘米)，可选
    :return: 推荐尺码信息
    """
    # 根据体重查找合适的尺码
    for size_range in size_table:
        if size_range['weight_min'] <= weight_jin <= size_range['weight_max']:
            return {
                'letter_size': size_range['letter_size'],
                'number_size': size_range['number_size']
            }
            
    raise ValueError("无法找到匹配的尺码")

def main(content: str = "", size_data: list = None) -> dict:
    """
    尺码推荐主函数
    :param content: 输入字符串，包含身高体重信息
    :param size_data: DIFY格式的尺码表数据列表
    :return: 包含推荐结果的字典
    """
    try:
        # 检查输入参数
        if not content:
            return {'result': "请输入体重信息，例如: '体重120斤'"}
            
        if not size_data:
            return {'result': "请提供尺码表数据"}
            
        # 解析用户输入
        params = parse_input_string(content)
        weight_jin = params['weight_jin']
        height_cm = params.get('height_cm')
        
        # 解析尺码表
        size_table = parse_size_table(size_data)
        
        # 获取推荐尺码
        size_info = recommend_size(weight_jin, size_table, height_cm)
        
        # 构建结果字符串
        if height_cm:
            result_str = (
                f"身高: {height_cm}cm, "
                f"体重: {weight_jin}斤, "
                f"推荐尺码: {size_info['letter_size']}码({size_info['number_size']})"
            )
        else:
            # 检查字母尺码和数字尺码是否相同
            if str(size_info['letter_size']) == str(size_info['number_size']):
                result_str = (
                    f"体重: {weight_jin}斤, "
                    f"推荐尺码: {size_info['number_size']}码"
                )
            else:
                result_str = (
                    f"体重: {weight_jin}斤, "
                    f"推荐尺码: {size_info['letter_size']}码({size_info['number_size']})"
                )
        
        return {'result': result_str}
        
    except Exception as e:
        return {'result': f"推荐出错: {str(e)}"}

# 测试代码
if __name__ == "__main__":
    # 测试用例 - 有身高信息
    test_content1 = "推荐尺码：身高170cm，体重146斤。"
    # 测试用例 - 无身高信息
    test_content2 = "尺码匹配：夹克 体重146斤"
    
    test_size_data = [
        {
            "content": """夹克：尺码推荐
            体重110-130斤的推荐M码（48码）
            体重130-145斤的推荐L码（50码）
            体重145-160斤的推荐XL码（52码）
            体重160-175斤的推荐2XL码（54码）
            体重175-190斤的推荐3XL码（56码）
            体重190-210斤的推荐4XL码（58码）"""
        }
    ]
    
    # 测试有身高信息的情况
    result1 = main(content=test_content1, size_data=test_size_data)
    print(f"有身高信息: {result1['result']}")
    
    # 测试无身高信息的情况
    result2 = main(content=test_content2, size_data=test_size_data)
    print(f"无身高信息: {result2['result']}")

    # 测试用例 - 墨鱼牛仔裤格式1
    test_content3 = "尺码匹配：体重96斤"
    test_size_data3 = [
        {
            "content": """牛仔裤，尺码匹配
            体重范围：80-95斤，推荐25码
            体重范围：96-105斤，推荐26码（S码）
            体重范围：106-125斤，推荐27码（M码）
            体重范围：116-125斤，推荐28码（L码）
            体重范围：126-135斤，推荐29码（XL码）
            体重范围：136-145斤，推荐30码"""
        }
    ]
    
    # 测试墨鱼牛仔裤格式1
    result3 = main(content=test_content3, size_data=test_size_data3)
    print(f"墨鱼牛仔裤1: {result3['result']}")
    
    # 测试用例 - 墨鱼牛仔裤格式2
    test_content4 = "尺码匹配：体重91斤"
    test_size_data4 = [
        {
            "content": """牛仔裤，尺码匹配
            体重范围：80-95斤，推荐XS码（25码）
            体重范围：96-105斤，推荐S码（26码）
            体重范围：106-125斤，推荐M码（27码）
            体重范围：116-125斤，推荐L码（28码）
            体重范围：126-135斤，推荐XL码（29码）
            体重范围：136-145斤，推荐XXL码（30码）"""
        }
    ]
    
    # 测试墨鱼牛仔裤格式2
    result4 = main(content=test_content4, size_data=test_size_data4)
    print(f"墨鱼牛仔裤2: {result4['result']}") 