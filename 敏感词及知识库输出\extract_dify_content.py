import json

def extract_content_with_position_and_score(data, arg_index=0):
    """
    从输入数据中提取content字段内容、position和score信息
    :param data: 包含content字段、metadata和score的数据
    :param arg_index: 参数索引，用于处理多参数情况下的position映射
    :return: 提取的内容、position和score
    """
    try:
        # 处理字符串类型
        if isinstance(data, str):
            return [{
                "content": data,
                "position": None,
                "score": None,
                "position_map_key": None
            }]
            
        # 处理列表类型
        if isinstance(data, list):
            contents_with_info = []
            for item in data:
                if isinstance(item, dict) and "content" in item:
                    # 提取位置和分数
                    position = None
                    score = None
                    
                    if "metadata" in item:
                        if "position" in item["metadata"]:
                            position = item["metadata"]["position"]
                        if "score" in item["metadata"]:
                            score = item["metadata"]["score"]
                    
                    # 位置映射键
                    position_map_key = f"{arg_index}_{position}" if position is not None else None
                    
                    contents_with_info.append({
                        "content": item["content"],
                        "position": position,
                        "score": score,
                        "position_map_key": position_map_key
                    })
            return contents_with_info
            
        # 处理字典类型
        if isinstance(data, dict):
            # 直接检查content字段
            if "content" in data:
                position = None
                score = None
                
                if "metadata" in data:
                    if "position" in data["metadata"]:
                        position = data["metadata"]["position"]
                    if "score" in data["metadata"]:
                        score = data["metadata"]["score"]
                
                position_map_key = f"{arg_index}_{position}" if position is not None else None
                
                return [{
                    "content": data["content"],
                    "position": position,
                    "score": score,
                    "position_map_key": position_map_key
                }]
        
        return []
    except Exception as e:
        print(f"提取内容失败: {str(e)}")
        return []

def format_references(contents_with_info, sort_by="position", position_mapping=None):
    """
    格式化参考信息输出
    :param contents_with_info: 包含内容、position和score的列表
    :param sort_by: 排序依据，可以是 "position"、"score" 或 "position_mapping"
    :param position_mapping: 位置映射字典，键为"arg_index_position"，值为全局position
    :return: 格式化的参考信息文本
    """
    if position_mapping is None:
        position_mapping = {}
    
    # 应用映射或分配新的全局position
    result = []
    for i, item in enumerate(contents_with_info):
        # 获取映射的全局position
        global_position = None
        if sort_by == "position_mapping" and item["position_map_key"] and item["position_map_key"] in position_mapping:
            global_position = position_mapping[item["position_map_key"]]
        else:
            # 如果没有提供position或没有映射，则使用索引+1
            global_position = i + 1
        
        result.append({
            "global_position": global_position,
            "content": item["content"],
            "score": item["score"],
            "position": item["position"]
        })
    
    # 根据排序方式排序
    if sort_by == "score":
        # 按score降序排序（高分在前）
        result.sort(key=lambda x: float('-inf') if x["score"] is None else -float(x["score"]))
    elif sort_by == "position_mapping" or sort_by == "position":
        # 按全局position或原始position排序
        result.sort(key=lambda x: x["global_position"])
    
    # 重新分配序号（1, 2, 3...）
    for i, item in enumerate(result):
        item["global_position"] = i + 1
    
    # 格式化输出
    formatted_result = []
    for item in result:
        formatted_result.append(f"参考{item['global_position']}：\n{item['content']}")
    
    return "\n\n".join(formatted_result)

def main(arg1=None, arg2=None, arg3=None, sort_by="position", position_mapping=None) -> dict:
    """
    主函数，处理输入并返回格式化的参考信息
    :param arg1: 第一个参数的内容
    :param arg2: 第二个参数的内容
    :param arg3: 第三个参数的内容（可选）
    :param sort_by: 排序依据，可以是 "position"、"score" 或 "position_mapping"
    :param position_mapping: 位置映射字典，用于将arg_index+position映射到全局position
    :return: 包含result字段的字典，result为字符串类型
    """
    try:
        # 检查所有输入是否为空或None
        if arg1 is None and arg2 is None and arg3 is None:
            return {"result": "为空"}
            
        # 检查输入是否为空字典或内容为null的字典
        if isinstance(arg1, dict) and (not arg1 or arg1.get("content_text") is None):
            return {"result": "为空"}
            
        all_contents = []
        
        # 处理所有输入参数
        inputs = [(i, arg) for i, arg in enumerate([arg1, arg2, arg3]) if arg is not None]
        if not inputs:
            return {"result": "为空"}
        
        # 如果使用position_mapping排序且没有提供映射，我们尝试从输入构建一个合理的映射
        if sort_by == "position_mapping" and position_mapping is None:
            position_mapping = {}
            
            # 提取所有position信息
            all_positions = []
            for i, arg in inputs:
                contents = extract_content_with_position_and_score(arg, i)
                for item in contents:
                    if item["position"] is not None and item["position_map_key"] is not None:
                        all_positions.append((i, item["position"], item["position_map_key"]))
            
            # 为每个位置分配全局的position
            global_position = 1
            for i, pos, map_key in sorted(all_positions, key=lambda x: (x[0], x[1])):
                if map_key not in position_mapping:
                    position_mapping[map_key] = global_position
                    global_position += 1
        
        # 提取内容
        for i, arg in inputs:
            all_contents.extend(extract_content_with_position_and_score(arg, i))
        
        # 如果没有内容，返回空字符串
        if not all_contents:
            return {"result": "为空"}
            
        # 格式化输出
        formatted_result = format_references(all_contents, sort_by, position_mapping)
        
        # 确保返回的是字符串类型
        if not isinstance(formatted_result, str):
            formatted_result = str(formatted_result)
            
        return {"result": formatted_result}

    except Exception as e:
        return {"result": f"处理出错: {str(e)}"}

if __name__ == "__main__":
    # 当直接运行此脚本时，不执行任何测试代码
    pass