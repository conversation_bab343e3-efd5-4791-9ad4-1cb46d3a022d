import json

def extract_product_info(query_text: str) -> dict:
    """
    从查询文本中提取商品ID和商品名称
    :param query_text: 包含商品信息的查询文本
    :return: 包含提取结果的字典 {product_id, product_name}
    """
    try:
        # 从输入文本中提取商品ID
        id_start = query_text.find("商品ID：") + len("商品ID：")
        id_end = query_text.find("，", id_start)
        product_id = query_text[id_start:id_end]

        # 从输入文本中提取商品名
        name_start = query_text.find("商品名：") + len("商品名：")
        product_name = query_text[name_start:]

        return {
            'product_id': product_id,
            'product_name': product_name
        }
    except Exception as e:
        return {'error': f"提取商品信息失败: {str(e)}"}

def main(input_data: str = None, content: str = None) -> dict:
    """
    主函数，处理输入并返回提取结果
    :param input_data: JSON格式的输入数据
    :param content: 直接提供的内容文本
    :return: 包含提取结果的字典
    """
    try:
        # 如果直接提供了content，优先使用
        if content:
            query_text = content
        else:
            # 如果没有提供输入，使用默认测试数据
            if not input_data:
                input_data = '''{
                  "content": "咨询商品ID：488946234022，商品名：青岛啤酒鸿运当头355ml*12瓶装11度整箱送礼佳品"
                }'''
            
            # 解析JSON输入
            data = json.loads(input_data)
            query_text = data.get("content", "")
            
            if not query_text:
                return {'result': "输入数据格式错误，缺少content字段"}
        
        # 提取商品信息
        result = extract_product_info(query_text)
        
        if 'error' in result:
            return {'result': result['error']}
        
        # 构建输出字符串
        output = f"商品id：{result['product_id']}\n\n商品名：{result['product_name']}"
        
        return {'result': output}
    except json.JSONDecodeError:
        return {'result': "输入不是有效的JSON格式"}
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 程序入口点
if __name__ == "__main__":
    # 测试输入
    test_input = '''{
      "content": "咨询商品ID：488946234022，商品名：青岛啤酒鸿运当头355ml*12瓶装11度整箱送礼佳品"
    }'''
    
    # 获取结果
    result = main(test_input)
    
    # 打印结果
    print(result['result'])