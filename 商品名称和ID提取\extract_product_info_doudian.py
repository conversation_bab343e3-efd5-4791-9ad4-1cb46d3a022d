import re
import json

def extract_doudian_product_info(text: str) -> dict:
    """
    从抖店文本中提取商品ID、商品名、规格等信息。
    支持常见的中英文冒号、全角/半角分隔符。
    新增：若包含"的直播间"，优先提取"的直播间"后到"￥"前的内容为商品名；
    若包含"短视频"，提取"的短视频"后到"￥"前的内容为商品名。
    :param text: 包含商品信息的文本
    :return: 提取结果字典
    """
    result = {}
    # 预处理：移除换行符
    text = text.replace('\n', ' ')
    
    # 新增：的直播间到￥之间的内容（优先级最高）
    live_match = re.search(r'的直播间(.+?)￥', text)
    if live_match:
        result['product_name'] = live_match.group(1).strip()
        return result
    # 短视频到￥之间的内容
    dv_match = re.search(r'的短视频(.+?)￥', text)
    if dv_match:
        result['product_name'] = dv_match.group(1).strip()
        return result
    # 商品ID
    id_match = re.search(r'(商品ID|ID)[：: ]?([0-9]+)', text)
    if id_match:
        result['product_id'] = id_match.group(2)
    # 商品名 - 优化匹配模式，支持更多特殊字符
    name_match = re.search(r'(商品名|名称)[：: ]?([^，。？！\n]+?)(?=，|$|提问|？)', text)
    if name_match:
        result['product_name'] = name_match.group(2).strip()
    # 规格
    spec_match = re.search(r'(规格)[：: ]?([\u4e00-\u9fa5A-Za-z0-9_\-×xX\*\.\s]+)', text)
    if spec_match:
        result['spec'] = spec_match.group(2).strip()
    return result


def main(input_data=None, content=None) -> dict:
    """
    主函数，处理输入并返回提取结果
    :param input_data: JSON格式的输入数据
    :param content: 直接提供的内容文本
    :return: 包含提取结果的字典，result为字符串
    """
    try:
        if content:
            query_text = content if isinstance(content, str) else json.dumps(content, ensure_ascii=False)
        elif input_data:
            if isinstance(input_data, dict):
                query_text = json.dumps(input_data, ensure_ascii=False)
            else:
                query_text = str(input_data)
        else:
            # 默认测试样例
            query_text = '商品ID:123456789 商品名:抖店爆款洗发水 规格:500ml*2瓶'
        result = extract_doudian_product_info(query_text)
        if not result:
            return {'result': '未找到抖店商品信息'}
        # 按照指定顺序输出
        output_parts = []
        if 'product_id' in result:
            output_parts.append(f"商品ID：{result['product_id']}")
        if 'product_name' in result:
            output_parts.append(f"商品名：{result['product_name']}")
        if 'spec' in result:
            output_parts.append(f"规格：{result['spec']}")
        if output_parts:
            return {'result': '，'.join(output_parts)}
        return {'result': '未找到抖店商品信息'}
    except Exception as e:
        return {'result': f'处理出错: {str(e)}'}

if __name__ == "__main__":
    # 测试样例
    test_text = '来自星禾的直播间乐宜趣宝宝安静毛毡布撕撕书撕不烂早教益智启蒙严选童言童语乐园￥34.80已售79203'
    print(main(content=test_text)['result']) 