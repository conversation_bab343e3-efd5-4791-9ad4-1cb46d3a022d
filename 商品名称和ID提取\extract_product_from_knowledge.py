import json
import sys

def main(**kwargs):
    """
    主函数，从JSON数据中提取第一个商品的信息
    可以接收任意关键字参数，以适应工作流中的不同调用方式
    """
    try:
        # 获取输入数据，支持多种参数名
        input_data = None
        
        # 检查所有可能的参数
        possible_params = ['content_text', 'content', 'arg1', 'input_data']
        for param in possible_params:
            if param in kwargs and kwargs[param] is not None:
                input_data = kwargs[param]
                break
        
        # 如果没有找到有效的输入参数，返回空值
        if input_data is None:
            return {"result": ""}
        
        # 提取商品信息
        product_info = extract_product_info(input_data)
        
        # 返回结果，如果是错误或未找到信息则返回空字符串
        if product_info == "未找到商品信息" or product_info.startswith("处理数据出错"):
            return {"result": ""}
        else:
            return {"result": product_info}
        
    except Exception as e:
        # 返回空字符串
        return {"result": ""}

def extract_product_info(data):
    """
    从数据中提取商品信息
    :param data: 输入数据，可以是字典、列表或字符串
    :return: 提取的商品信息
    """
    try:
        # 处理字符串类型
        if isinstance(data, str):
            try:
                # 尝试解析为JSON
                parsed_data = json.loads(data)
                data = parsed_data
            except json.JSONDecodeError:
                # 如果不是JSON，直接返回文本
                return data.strip()
        
        # 处理列表类型 - 直接获取列表中第一项的content
        if isinstance(data, list) and len(data) > 0:
            first_item = data[0]
            if isinstance(first_item, dict) and 'content' in first_item:
                return first_item['content'].strip()
        
        # 处理字典类型 - 检查content_text字段
        if isinstance(data, dict):
            # 检查content_text字段
            if 'content_text' in data:
                content_list = data['content_text']
                if isinstance(content_list, list) and len(content_list) > 0:
                    first_item = content_list[0]
                    if isinstance(first_item, dict) and 'content' in first_item:
                        return first_item['content'].strip()
            
            # 检查content字段
            if 'content' in data:
                content_value = data['content']
                if isinstance(content_value, list) and len(content_value) > 0:
                    first_item = content_value[0]
                    if isinstance(first_item, dict) and 'content' in first_item:
                        return first_item['content'].strip()
                elif isinstance(content_value, str):
                    return content_value.strip()
            
            # 检查arg1字段
            if 'arg1' in data:
                arg_value = data['arg1']
                if isinstance(arg_value, list) and len(arg_value) > 0:
                    first_item = arg_value[0]
                    if isinstance(first_item, dict) and 'content' in first_item:
                        return first_item['content'].strip()
        
        # 未找到商品信息
        return "未找到商品信息"
        
    except Exception as e:
        return f"处理数据出错: {str(e)}"

if __name__ == "__main__":
    # 当直接运行此脚本时，不执行任何测试代码
    pass 