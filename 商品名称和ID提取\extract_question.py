import json

def extract_question(query_text: str) -> dict:
    """
    从查询文本中提取提问内容
    :param query_text: 包含提问信息的查询文本
    :return: 包含提取结果的字典 {question}
    """
    try:
        result = {}
        
        # 查找提问标记
        question_markers = ["提问：", "问："]
        
        for marker in question_markers:
            if marker in query_text:
                question_start = query_text.find(marker) + len(marker)
                # 查找可能的结束标记
                end_markers = ["\n", "A:", "答：", "#"]
                question_end = len(query_text)
                
                for end_marker in end_markers:
                    pos = query_text.find(end_marker, question_start)
                    if pos != -1 and pos < question_end:
                        question_end = pos
                
                question = query_text[question_start:question_end].strip()
                if question:
                    result['question'] = question
                    return result
        
        return result
    except Exception as e:
        return {'error': f"提取提问内容失败: {str(e)}"}

def main(input_data=None, content=None) -> dict:
    """
    主函数，处理输入并返回提取结果
    :param input_data: JSON格式的输入数据
    :param content: 直接提供的内容文本
    :return: 包含提取结果的字典
    """
    try:
        # 如果直接提供了content，优先使用
        if content:
            # 检查content是否已经是字典类型
            if isinstance(content, dict):
                content_data = content
            # 检查content是否已经是列表类型
            elif isinstance(content, list):
                # 直接处理列表中的每个项目
                for item in content:
                    if isinstance(item, dict) and 'content' in item:
                        result = extract_question(item['content'])
                        if 'question' in result:
                            return {'result': f"提问：{result['question']}"}
                return {'result': "未找到提问内容"}
            else:
                # 尝试解析content为JSON
                try:
                    content_data = json.loads(content)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接使用content作为查询文本
                    query_text = content
                    result = extract_question(query_text)
                    if 'error' in result:
                        return {'result': result['error']}
                    
                    # 构建输出字符串
                    if 'question' in result:
                        return {'result': f"提问：{result['question']}"}
                    else:
                        return {'result': "未找到提问内容"}
            
            # 处理JSON格式的content
            if isinstance(content_data, dict) and 'content' in content_data:
                if isinstance(content_data['content'], list):
                    # 从内容项中提取提问
                    for item in content_data['content']:
                        if 'content' in item and item['content']:
                            result = extract_question(item['content'])
                            if 'question' in result:
                                return {'result': f"提问：{result['question']}"}
                    return {'result': "未找到提问内容"}
                else:
                    query_text = str(content_data['content'])
            else:
                # 尝试直接从content中提取
                query_text = str(content)
        else:
            # 如果没有提供输入，使用默认测试数据
            if not input_data:
                input_data = '''{
                  "content": "咨询商品ID：629906807171，商品名：便签纸定制可撕学生手写a4信纸空白草稿纸加厚办公便携大号便笺本，提问： 什么快递？啥时候发？"
                }'''
            
            # 检查input_data是否已经是字典类型
            if isinstance(input_data, dict):
                data = input_data
            # 检查input_data是否已经是列表类型
            elif isinstance(input_data, list):
                # 直接处理列表中的每个项目
                for item in input_data:
                    if isinstance(item, dict) and 'content' in item:
                        result = extract_question(item['content'])
                        if 'question' in result:
                            return {'result': f"提问：{result['question']}"}
                return {'result': "未找到提问内容"}
            else:
                # 解析JSON输入
                try:
                    data = json.loads(input_data)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接使用input_data作为查询文本
                    query_text = str(input_data)
                    result = extract_question(query_text)
                    if 'error' in result:
                        return {'result': result['error']}
                    
                    # 构建输出字符串
                    if 'question' in result:
                        return {'result': f"提问：{result['question']}"}
                    else:
                        return {'result': "未找到提问内容"}
            
            # 检查是否有content字段，且是列表
            if isinstance(data, dict) and 'content' in data:
                if isinstance(data['content'], list):
                    # 从内容项中提取提问
                    for item in data['content']:
                        if 'content' in item and item['content']:
                            result = extract_question(item['content'])
                            if 'question' in result:
                                return {'result': f"提问：{result['question']}"}
                    return {'result': "未找到提问内容"}
                else:
                    query_text = str(data['content'])
            else:
                return {'result': "输入数据格式错误，缺少content字段"}
        
        # 提取提问内容
        result = extract_question(query_text)
        
        if 'error' in result:
            return {'result': result['error']}
        
        # 构建输出字符串
        if 'question' in result:
            output = f"提问：{result['question']}"
        else:
            output = "未找到提问内容"
        
        return {'result': output}
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 程序入口点
if __name__ == "__main__":
    # 测试输入
    test_input = '''{
      "content": "咨询商品ID：629906807171，商品名：便签纸定制可撕学生手写a4信纸空白草稿纸加厚办公便携大号便笺本，提问： 什么快递？啥时候发？"
    }'''
    
    # 获取结果
    result = main(test_input)
    
    # 打印结果
    print(result['result']) 