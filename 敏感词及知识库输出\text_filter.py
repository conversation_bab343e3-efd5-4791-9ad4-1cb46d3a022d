import json
from typing import Union

def parse_filters(filters_text: str) -> dict:
    """
    解析过滤规则文本，转换为替换规则字典
    :param filters_text: 包含替换规则的文本
    :return: 替换规则字典
    """
    replacements = {}
    try:
        # 按行分割规则
        rules = filters_text.strip().split('\n')
        for rule in rules:
            if '=' in rule:
                key, value = rule.split('=', 1)
                # 去除引号
                value = value.strip('"')
                replacements[key] = value
        return replacements
    except Exception as e:
        print(f"解析过滤规则失败: {str(e)}")
        return {}

def apply_filters(text: str, replacements: dict) -> str:
    """
    应用过滤规则，替换或删除指定内容
    :param text: 输入文本
    :param replacements: 替换规则字典
    :return: 处理后的文本
    """
    result = text
    for key, value in replacements.items():
        if value:  # 如果有替换值
            result = result.replace(key, value)
        else:  # 如果替换值为空，则删除该内容
            result = result.replace(key, '')
    return result

def process_input(data: dict) -> dict:
    """
    处理输入数据
    :param data: 输入的字典数据
    :return: 处理结果
    """
    try:
        content = data.get('content', '')
        
        # 如果没有content，返回错误
        if not content:
            return {'result': "输入数据格式错误，缺少content字段"}
            
        # 如果没有filters，直接返回原文本
        filters = data.get('filters', '')
        if not filters:
            return {'result': content}
            
        # 解析过滤规则
        replacements = parse_filters(filters)
        if not replacements:
            # 如果解析失败，返回原文本
            return {'result': content}
            
        # 应用过滤规则
        filtered_text = apply_filters(content, replacements)
        return {'result': filtered_text}
        
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

def main(input_data: Union[str, dict] = None, **kwargs) -> dict:
    """
    主函数，处理输入并返回过滤结果
    :param input_data: JSON格式的字符串或字典数据
    :param kwargs: 其他关键字参数（支持直接传入content和filters）
    :return: 包含处理结果的字典
    """
    try:
        # 如果通过kwargs提供了参数
        if 'content' in kwargs:
            data = {
                'content': kwargs['content']
            }
            # 如果提供了filters，添加到数据中
            if 'filters' in kwargs:
                data['filters'] = kwargs['filters']
            return process_input(data)
            
        # 如果提供了input_data
        if input_data:
            # 如果是字典类型，直接使用
            if isinstance(input_data, dict):
                return process_input(input_data)
            # 如果是字符串，尝试解析JSON
            elif isinstance(input_data, str):
                data = json.loads(input_data)
                return process_input(data)
                
        # 如果没有提供任何输入，使用默认测试数据
        default_input = '''{
          "content": "请通过支付宝或微信支付付款，有问题可以联系微信号：abc123，或者QQ：456789",
          "filters": "支付宝=\\"官方\\"\\n微信支付=\\"官方\\"\\n财付通=\\"官方\\"\\n京东支付=\\"官方\\"\\n抖店支付=\\"官方\\"\\n手机号=\\"\\"\\n微信号=\\"\\"\\n联系方式=\\"\\"\\nQQ=\\"\\"\\n微信=\\"\\"" 
        }'''
        data = json.loads(default_input)
        return process_input(data)

    except json.JSONDecodeError:
        return {'result': "输入不是有效的JSON格式"}
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 程序入口点
if __name__ == "__main__":
    # 测试输入 - 只有content
    test_input1 = {
        "content": "请通过支付宝或微信支付付款，有问题可以联系微信号：abc123，或者QQ：456789"
    }
    
    # 测试输入 - 完整数据
    test_input2 = {
        "content": "请通过支付宝或微信支付付款，有问题可以联系微信号：abc123，或者QQ：456789",
        "filters": "支付宝=\"官方\"\n微信支付=\"官方\"\n财付通=\"官方\"\n京东支付=\"官方\"\n抖店支付=\"官方\"\n手机号=\"\"\n微信号=\"\"\n联系方式=\"\"\nQQ=\"\"\n微信=\"\""
    }
    
    # 测试不同的调用方式
    result1 = main(test_input1)  # 只有content
    result2 = main(test_input2)  # 完整数据
    result3 = main(content="测试文本")  # 只传content参数
    
    # 打印结果
    print("只有content的结果:", result1['result'])
    print("完整数据的结果:", result2['result'])

    # 输出结果示例：
    # "请通过官方或官方付款，有问题可以联系，或者" 