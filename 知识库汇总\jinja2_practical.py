#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jinja2实用示例：处理不同数据类型的变量合并
"""

from jinja2 import Template
import json

def demo_string_merge():
    """字符串合并示例"""
    print("=== 字符串合并 ===")
    template = Template("{{ arg1 + arg2 }}")
    
    result = template.render(arg1="Hello", arg2="World")
    print(f"结果: {result}")

def demo_number_merge():
    """数字合并示例"""
    print("\n=== 数字合并 ===")
    # 数字相加
    template_add = Template("{{ arg1 + arg2 }}")
    # 数字转字符串拼接
    template_concat = Template("{{ arg1|string + arg2|string }}")
    
    result_add = template_add.render(arg1=10, arg2=20)
    result_concat = template_concat.render(arg1=10, arg2=20)
    
    print(f"数字相加: {result_add}")
    print(f"数字拼接: {result_concat}")

def demo_list_merge():
    """列表合并示例"""
    print("\n=== 列表合并 ===")
    # 列表拼接
    template_concat = Template("{{ arg1 + arg2 }}")
    # 列表元素连接
    template_join = Template("{{ (arg1 + arg2) | join(', ') }}")
    
    list1 = ["apple", "banana"]
    list2 = ["orange", "grape"]
    
    result_concat = template_concat.render(arg1=list1, arg2=list2)
    result_join = template_join.render(arg1=list1, arg2=list2)
    
    print(f"列表拼接: {result_concat}")
    print(f"元素连接: {result_join}")

def demo_dict_merge():
    """字典合并示例"""
    print("\n=== 字典合并 ===")
    template = Template("""
{%- set merged = arg1.copy() -%}
{%- for key, value in arg2.items() -%}
{%- set _ = merged.update({key: value}) -%}
{%- endfor -%}
{{ merged | tojson }}
""")
    
    dict1 = {"name": "张三", "age": 25}
    dict2 = {"city": "北京", "job": "程序员"}
    
    result = template.render(arg1=dict1, arg2=dict2)
    print(f"字典合并: {result}")

def demo_conditional_merge():
    """条件合并示例"""
    print("\n=== 条件合并 ===")
    template = Template("""
{%- if arg1 is string and arg2 is string -%}
字符串合并: {{ arg1 + arg2 }}
{%- elif arg1 is number and arg2 is number -%}
数字相加: {{ arg1 + arg2 }}
{%- elif arg1 is iterable and arg2 is iterable -%}
列表合并: {{ arg1 + arg2 }}
{%- else -%}
转换为字符串合并: {{ arg1|string + arg2|string }}
{%- endif -%}
""")
    
    test_cases = [
        ("Hello", "World"),
        (10, 20),
        ([1, 2], [3, 4]),
        ("混合", 123)
    ]
    
    for arg1, arg2 in test_cases:
        result = template.render(arg1=arg1, arg2=arg2)
        print(f"输入: {arg1}, {arg2} -> {result.strip()}")

def demo_safe_merge():
    """安全合并示例（处理None值）"""
    print("\n=== 安全合并 ===")
    template = Template("""
{%- set val1 = arg1 if arg1 is not none else '' -%}
{%- set val2 = arg2 if arg2 is not none else '' -%}
{{ val1 + val2 }}
""")
    
    test_cases = [
        ("Hello", "World"),
        ("Hello", None),
        (None, "World"),
        (None, None)
    ]
    
    for arg1, arg2 in test_cases:
        result = template.render(arg1=arg1, arg2=arg2)
        print(f"输入: {arg1}, {arg2} -> '{result}'")

def main():
    print("Jinja2变量合并实用示例")
    print("=" * 50)
    
    demo_string_merge()
    demo_number_merge()
    demo_list_merge()
    demo_dict_merge()
    demo_conditional_merge()
    demo_safe_merge()

if __name__ == "__main__":
    main()
