import json
import sys

def main(**kwargs):
    """
    主函数，从JSON数据中提取商品名称
    可以接收任意关键字参数，以适应工作流中的不同调用方式
    """
    try:
        # 获取输入数据，支持多种参数名
        input_data = None
        
        # 检查所有可能的参数
        possible_params = ['content_text', 'content', 'arg1', 'input_data']
        for param in possible_params:
            if param in kwargs and kwargs[param] is not None:
                input_data = kwargs[param]
                break
        
        # 如果没有找到有效的输入参数，使用默认测试数据
        if input_data is None:
            # 默认测试数据
            input_data = {
                "content": "咨询商品ID：749358617194，商品名：联想小新Pro14 2025锐龙7 H 255 24GB 1TB 集显轻薄笔记本电脑【3天内发货】"
            }
        
        # 提取商品名称
        product_name = extract_product_name(input_data)
        
        # 返回结果，如果是错误或未找到信息则返回空字符串
        if product_name == "未找到商品名称" or product_name.startswith("处理数据出错"):
            return {"result": ""}
        else:
            # 去除所有换行符
            product_name = product_name.replace('\n', '').strip()
            return {"result": product_name}
        
    except Exception as e:
        # 返回空字符串
        return {"result": ""}

def extract_product_name(data):
    """
    从数据中提取商品名称
    :param data: 输入数据，可以是字典、列表或字符串
    :return: 提取的商品名称
    """
    try:
        # 处理字符串类型
        if isinstance(data, str):
            # 尝试解析JSON字符串
            try:
                parsed_data = json.loads(data)
                data = parsed_data
            except json.JSONDecodeError:
                # 如果不是JSON，尝试直接提取商品名
                if "商品名" in data:
                    # 处理转义字符
                    content = data.replace('\\"', '"')
                    if "商品名" in content:
                        return content.split("商品名")[1].strip('":\\')
                return data.strip()
        
        # 处理列表类型
        if isinstance(data, list) and len(data) > 0:
            first_item = data[0]
            if isinstance(first_item, dict) and 'content' in first_item:
                content = first_item['content']
                if "商品名" in content:
                    return content.split("商品名")[1].strip('":\\')
                return content.strip()
        
        # 处理字典类型
        if isinstance(data, dict):
            # 检查content_text字段
            if 'content_text' in data:
                content = data['content_text']
                if isinstance(content, str):
                    # 处理换行符
                    lines = [line.strip() for line in content.split('\n') if line.strip()]
                    for line in lines:
                        if line.startswith('商品名'):
                            return line.replace('商品名', '').strip()
                    return content.strip()
            
            # 检查content字段
            if 'content' in data:
                content = data['content']
                if isinstance(content, str):
                    if "商品名：" in content:
                        return content.split("商品名：")[1].strip()
                    elif "商品名" in content:
                        return content.split("商品名")[1].strip('":\\')
                    return content.strip()
                elif isinstance(content, list) and len(content) > 0:
                    first_item = content[0]
                    if isinstance(first_item, dict) and 'content' in first_item:
                        item_content = first_item['content']
                        if "商品名" in item_content:
                            return item_content.split("商品名")[1].strip('":\\')
                        return item_content.strip()
        
        # 未找到商品名称
        return "未找到商品名称"
        
    except Exception as e:
        return f"处理数据出错: {str(e)}"

if __name__ == "__main__":
    # 测试用例1：带换行符的content_text格式
    test_data1 = {
        "content_text": "商品名\n刻章刻字印章订做制盖章定刻椭长方形圆形章子奖状奖励学生竣工图"
    }
    
    # 测试用例2：带商品ID的content格式
    test_data2 = {
        "content": "咨询商品ID：749358617194，商品名：联想小新Pro14 2025锐龙7 H 255 24GB 1TB 集显轻薄笔记本电脑【3天内发货】"
    }
    
    # 运行测试1
    print("测试用例1结果：")
    result1 = main(arg1=test_data1)
    print(result1['result'])
    
    # 运行测试2
    print("\n测试用例2结果：")
    result2 = main(arg1=test_data2)
    print(result2['result']) 