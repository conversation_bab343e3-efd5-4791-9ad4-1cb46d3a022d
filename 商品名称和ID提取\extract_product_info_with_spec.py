import json

def extract_product_info(query_text: str) -> dict:
    """
    从查询文本中提取商品名称和规格信息
    :param query_text: 包含商品信息的查询文本
    :return: 包含提取结果的字典 {product_name, specification}
    """
    try:
        result = {}
        
        # 处理特殊格式："商品名\":\"" (处理转义字符)
        if "商品名" in query_text:
            patterns = ["商品名\":\"", "商品名\\\":\\\""]
            for pattern in patterns:
                if pattern in query_text:
                    name_start = query_text.find(pattern) + len(pattern)
                    # 查找结束位置 (可能是 " 或 \")
                    for end_pattern in ["\"", "\\\"", "\";"]:
                        name_end = query_text.find(end_pattern, name_start)
                        if name_end != -1:
                            product_name = query_text[name_start:name_end]
                            result['product_name'] = product_name
                            break
        
        # 处理规格信息："规格\":\"" (处理转义字符)
        if "规格" in query_text:
            patterns = ["规格\":\"", "规格\\\":\\\""]
            for pattern in patterns:
                if pattern in query_text:
                    spec_start = query_text.find(pattern) + len(pattern)
                    # 查找结束位置 (可能是 " 或 \")
                    for end_pattern in ["\"", "\\\"", "\";"]:
                        spec_end = query_text.find(end_pattern, spec_start)
                        if spec_end != -1:
                            specification = query_text[spec_start:spec_end]
                            result['specification'] = specification
                            break

        return result
    except Exception as e:
        return {'error': f"提取商品信息失败: {str(e)}"}

def process_input(input_data):
    """
    处理输入数据，提取商品信息
    :param input_data: 输入的JSON数据或字符串
    :return: 提取结果的字典
    """
    try:
        # 判断输入是字符串还是已解析的数据
        if isinstance(input_data, str):
            try:
                data = json.loads(input_data)
            except json.JSONDecodeError:
                # 如果不是JSON，直接作为查询文本处理
                result = extract_product_info(input_data)
                return format_output(result)
        else:
            data = input_data
        
        # 处理包含query_text字段的数据
        if isinstance(data, dict) and 'query_text' in data:
            if isinstance(data['query_text'], list):
                results = []
                for item in data['query_text']:
                    if 'content' in item:
                        result = extract_product_info(item['content'])
                        if result and ('product_name' in result or 'specification' in result):
                            results.append(format_output(result))
                return results
        
        # 处理其他可能的格式
        if isinstance(data, dict) and 'content' in data:
            if isinstance(data['content'], list):
                results = []
                for item in data['content']:
                    if 'content' in item:
                        result = extract_product_info(item['content'])
                        if result and ('product_name' in result or 'specification' in result):
                            results.append(format_output(result))
                return results
            else:
                result = extract_product_info(data['content'])
                return format_output(result)
        
        # 如果是简单的字符串内容
        return format_output(extract_product_info(str(data)))
        
    except Exception as e:
        return {'error': f"处理数据出错: {str(e)}"}

def format_output(result):
    """
    格式化输出结果
    :param result: 包含提取结果的字典
    :return: 格式化后的字符串或字典
    """
    if 'error' in result:
        return {'result': result['error']}
    
    output_parts = []
    if 'product_name' in result:
        output_parts.append(f"商品名：{result['product_name']}")
    if 'specification' in result:
        output_parts.append(f"规格：{result['specification']}")
    
    if output_parts:
        return {'result': '，'.join(output_parts)}
    else:
        return {'result': "未找到商品信息和规格"}

def main(content=None):
    """
    主函数，处理输入内容并提取商品信息
    :param content: 可选的输入内容，如果提供则处理该内容，否则使用测试数据
    """
    if content is not None:
        # 处理传入的内容
        results = process_input(content)
    else:
        # 使用测试输入
        test_input = '''
        {
          "query_text": [
            {
              "metadata": {
                "_source": "knowledge",
                "dataset_id": "d8e263cd-5818-49bb-87f7-60396463fc86",
                "dataset_name": "易洁餐具-商品识别",
                "document_id": "a5d3b055-2858-49ed-8149-b8eb0e268a46",
                "document_name": "识别商品及规格.xlsx",
                "document_data_source_type": "upload_file",
                "segment_id": "a88fc0e0-001c-4770-b738-2538f7880d5d",
                "retriever_from": "workflow",
                "score": 0.6694101,
                "segment_hit_count": 1,
                "segment_word_count": 33,
                "segment_position": 11,
                "segment_index_node_hash": "4dd0bb95bc09a3178fac925ee1a84020ba5bc3bdb9af207c11adc5cc55c3ad78",
                "doc_metadata": {
                  "source": "upload_file",
                  "uploader": "jondfu",
                  "upload_date": "2025-04-30 16:38:18",
                  "document_name": "识别商品及规格.xlsx",
                  "last_update_date": "2025-04-30 16:38:18"
                },
                "position": 1
              },
              "title": "识别商品及规格.xlsx",
              "content": "商品名\\\":\\\"一次性餐盒餐盒\\\";\\\"规格\\\":\\\"500方盒/毫升/ml\\\""
            },
            {
              "metadata": {
                "_source": "knowledge",
                "dataset_id": "d8e263cd-5818-49bb-87f7-60396463fc86",
                "dataset_name": "易洁餐具-商品识别",
                "document_id": "a5d3b055-2858-49ed-8149-b8eb0e268a46",
                "document_name": "识别商品及规格.xlsx",
                "document_data_source_type": "upload_file",
                "segment_id": "3ebae2f5-197a-4e83-991b-e402205c1d61",
                "retriever_from": "workflow",
                "score": 0.6537017199999999,
                "segment_hit_count": 1,
                "segment_word_count": 33,
                "segment_position": 3,
                "segment_index_node_hash": "551d25f72df2fbd0bd48c3f04ebaacdcb80b97254ea09b911df8068de5167711",
                "doc_metadata": {
                  "source": "upload_file",
                  "uploader": "jondfu",
                  "upload_date": "2025-04-30 16:38:18",
                  "document_name": "识别商品及规格.xlsx",
                  "last_update_date": "2025-04-30 16:38:18"
                },
                "position": 2
              },
              "title": "识别商品及规格.xlsx",
              "content": "商品名\\\":\\\"一次性餐盒餐盒\\\";\\\"规格\\\":\\\"500圆碗/毫升/ml\\\""
            }
          ]
        }
        '''
        results = process_input(test_input)
    
    # 打印结果
    if isinstance(results, list):
        for i, result in enumerate(results, 1):
            print(f"结果 {i}: {result['result']}")
    else:
        print(results['result'])
    
    return results

if __name__ == "__main__":
    main() 