#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Jinja2模板示例：合并两个变量输入
"""

from jinja2 import Template

# 方法1：简单的字符串拼接
template1 = Template("{{ arg1 + arg2 }}")

# 方法2：使用过滤器进行字符串连接
template2 = Template("{{ [arg1, arg2] | join('') }}")

# 方法3：使用过滤器进行字符串连接（带分隔符）
template3 = Template("{{ [arg1, arg2] | join(' ') }}")

# 方法4：更复杂的合并格式
template4 = Template("合并结果：{{ arg1 }}{{ arg2 }}")

# 方法5：条件合并
template5 = Template("""
{%- if arg1 and arg2 -%}
{{ arg1 + arg2 }}
{%- elif arg1 -%}
{{ arg1 }}
{%- elif arg2 -%}
{{ arg2 }}
{%- else -%}
无输入
{%- endif -%}
""")

def main():
    # 测试数据
    test_cases = [
        {"arg1": "Hello", "arg2": "World"},
        {"arg1": "你好", "arg2": "世界"},
        {"arg1": "123", "arg2": "456"},
        {"arg1": "", "arg2": "只有arg2"},
        {"arg1": "只有arg1", "arg2": ""},
        {"arg1": "", "arg2": ""},
    ]
    
    print("Jinja2变量合并示例")
    print("=" * 50)
    
    for i, data in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: arg1='{data['arg1']}', arg2='{data['arg2']}'")
        print("-" * 30)
        
        # 方法1：直接拼接
        result1 = template1.render(**data)
        print(f"方法1 (直接拼接): '{result1}'")
        
        # 方法2：join过滤器
        result2 = template2.render(**data)
        print(f"方法2 (join过滤器): '{result2}'")
        
        # 方法3：带空格分隔
        result3 = template3.render(**data)
        print(f"方法3 (空格分隔): '{result3}'")
        
        # 方法4：格式化输出
        result4 = template4.render(**data)
        print(f"方法4 (格式化): '{result4}'")
        
        # 方法5：条件合并
        result5 = template5.render(**data)
        print(f"方法5 (条件合并): '{result5}'")

if __name__ == "__main__":
    main()
