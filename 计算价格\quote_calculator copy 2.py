def calculate_price(length_cm: float, width_cm: float, quantity: int, price_data: dict = None) -> float:
    """
    计算最终报价
    :param length_cm: 长度(厘米)
    :param width_cm: 宽度(厘米)
    :param quantity: 数量
    :param price_data: 价格数据字典，包含 original_price(原价)、tier1_price(阶梯价1)、tier2_price(阶梯价2)
    :return: 最终报价(元)
    """
    # 设置默认价格
    default_prices = {
        'original_price': 170,  # 原价
        'tier1_price': 120,    # 阶梯价1
        'tier2_price': 60      # 阶梯价2
    }
    
    # 如果提供了价格数据，则更新默认值
    if price_data:
        default_prices.update(price_data)
    
    # 1. 计算有效面积（含1cm裁边补偿）
    area_m2 = ((length_cm + 1) * (width_cm + 1) * quantity) / 10000
    
    # 2. 计算原始价格（用于判断阶梯）
    original_price = area_m2 * default_prices['original_price']
    
    # 3. 应用阶梯定价
    if original_price < 20:
        final_price = 20
    elif original_price <= 60:
        final_price = area_m2 * default_prices['tier1_price']
    else:
        final_price = area_m2 * default_prices['tier2_price']
    
    # 4. 四舍五入并保底20元
    return max(20, round(final_price))

def parse_input_string(input_str: str) -> dict:
    """
    从字符串输入中提取尺寸和数量，支持多种自然语言格式
    :param input_str: 输入字符串，支持多种格式（包括直径格式）
    :return: 包含提取结果的字典 {length_cm, width_cm, quantity, is_diameter}
    """
    try:
        import re
        import math
        
        # 清理输入字符串
        input_str = input_str.lower().strip()
        
        # 检查是否是直径格式
        diameter_match = re.search(r'直径\s*[：:]?\s*(\d+(?:\.\d+)?)', input_str)
        if diameter_match:
            diameter_cm = float(diameter_match.group(1))
            # 将直径转换为正方形的边长（保持面积一致）
            # 圆形面积 = π * (d/2)²
            # 正方形面积 = side²
            # 所以 side = √(π * (d/2)²)
            side_cm = math.sqrt(math.pi * (diameter_cm/2) ** 2)
            length_cm = side_cm
            width_cm = side_cm
            is_diameter = True
        else:
            # 尝试找到直径相关的数字格式
            diameter_matches = re.findall(r'[φΦ⌀]?\s*(\d+(?:\.\d+)?)\s*(?:cm|厘米)?(?:\s*直径)?', input_str)
            if '直径' in input_str and diameter_matches:
                diameter_cm = float(diameter_matches[0])
                side_cm = math.sqrt(math.pi * (diameter_cm/2) ** 2)
                length_cm = side_cm
                width_cm = side_cm
                is_diameter = True
            else:
                # 提取长度和宽度（非直径情况）
                # 支持"长XXcm*宽YYcm"格式
                length_match = re.search(r'长[度]?\s*[：:]?\s*(\d+(?:\.\d+)?)', input_str)
                width_match = re.search(r'宽[度]?\s*[：:]?\s*(\d+(?:\.\d+)?)', input_str)
                
                if length_match and width_match:
                    length_cm = float(length_match.group(1))
                    width_cm = float(width_match.group(1))
                    is_diameter = False
                else:
                    # 如果没有明确的长宽标识，尝试其他格式
                    numbers = re.findall(r'\d+(?:\.\d+)?', input_str)
                    if len(numbers) < 2:
                        # 仅有一个数字，可能是直径
                        if len(numbers) == 1 and ('圆' in input_str or '直径' in input_str or 'φ' in input_str or 'Φ' in input_str or '⌀' in input_str):
                            diameter_cm = float(numbers[0])
                            side_cm = math.sqrt(math.pi * (diameter_cm/2) ** 2)
                            length_cm = side_cm
                            width_cm = side_cm
                            is_diameter = True
                        else:
                            raise ValueError("无法找到足够的尺寸数据，请提供长度和宽度或直径")
                    else:
                        # 检查是否有明确的分隔符
                        if '*' in input_str or 'x' in input_str or '×' in input_str:
                            # 优先提取使用分隔符明确标识的尺寸
                            for separator in ['*', 'x', '×']:
                                if separator in input_str:
                                    parts = input_str.split(separator, 1)
                                    width_numbers = re.findall(r'\d+(?:\.\d+)?', parts[0])
                                    length_numbers = re.findall(r'\d+(?:\.\d+)?', parts[1])
                                    if width_numbers and length_numbers:
                                        width_cm = float(width_numbers[-1])
                                        length_cm = float(length_numbers[0])
                                        is_diameter = False
                                        break
                            else:
                                # 如果没有找到分隔符，使用前两个数字作为宽度和长度
                                width_cm = float(numbers[0])
                                length_cm = float(numbers[1])
                                is_diameter = False
                        else:
                            # 没有明确的分隔符，假设前两个数字分别是宽度和长度
                            width_cm = float(numbers[0])
                            length_cm = float(numbers[1])
                            is_diameter = False
        
        # 提取数量
        quantity = 1  # 默认数量
        # 查找包含"数量"相关词的部分
        match = re.search(r'数量\s*[：:]?\s*(\d+)|(\d+)\s*[个只份]', input_str)
        if match:
            # 如果找到了明确的数量标识
            groups = match.groups()
            for group in groups:
                if group and group.isdigit():
                    quantity = int(group)
                    break
        elif len(re.findall(r'\d+(?:\.\d+)?', input_str)) > (1 if is_diameter else 2):
            # 如果没有明确标识但有额外的数字，假设是数量
            numbers = re.findall(r'\d+(?:\.\d+)?', input_str)
            quantity_index = 1 if is_diameter else 2
            if len(numbers) > quantity_index:
                quantity = int(float(numbers[quantity_index]))
        
        return {
            'length_cm': length_cm,
            'width_cm': width_cm,
            'quantity': quantity,
            'is_diameter': is_diameter
        }
        
    except Exception as e:
        raise ValueError(f"解析输入失败: {str(e)}")

def parse_price_data(price_data_list: list) -> dict:
    """
    从DIFY输入格式中解析价格数据
    :param price_data_list: DIFY格式的价格数据列表
    :return: 解析后的价格字典
    """
    # 默认价格
    default_prices = {
        'original_price': 170,  # 原价
        'tier1_price': 120,    # 阶梯价1
        'tier2_price': 60      # 阶梯价2
    }
    
    if not price_data_list or not isinstance(price_data_list, list):
        return default_prices
        
    # 从price_data列表中获取第一个元素的content
    try:
        content = price_data_list[0].get('content', '')
        if not content:
            return default_prices
            
        # 解析价格数据
        import re
        prices = {
            'original_price': None,
            'tier1_price': None,
            'tier2_price': None
        }
        
        # 使用正则表达式提取价格
        patterns = {
            'original_price': r'原价.*?(\d+)元',
            'tier1_price': r'阶梯价格1.*?(\d+)元',
            'tier2_price': r'阶梯价格2.*?(\d+)元'
        }
        
        for key, pattern in patterns.items():
            match = re.search(pattern, content)
            if match:
                prices[key] = int(match.group(1))
                
        # 更新默认价格
        return {k: v if v is not None else default_prices[k] for k, v in prices.items()}
        
    except Exception as e:
        print(f"解析价格数据时出错: {str(e)}")
        return default_prices

def main(content: str = "", price_data: list = None) -> dict:
    """
    计算报价的主函数
    :param content: 输入字符串，支持多种格式
    :param price_data: DIFY格式的价格数据列表
    :return: 包含计算结果的字典，result为字符串
    """
    try:
        # 解析价格数据
        prices = parse_price_data(price_data)
        
        # 检查是否有content参数
        if content:
            try:
                params = parse_input_string(content)
                length_cm = params['length_cm']
                width_cm = params['width_cm']
                quantity = params['quantity']
                is_diameter = params.get('is_diameter', False)
            except ValueError as e:
                return {'result': f"输入解析错误: {str(e)}"}
        else:
            return {'result': "请输入尺寸和数量，例如: '20*30 100个' 或 '长宽：20cm*30cm，数量50个' 或 '直径25cm，数量100'"}
        
        # 参数验证
        if length_cm <= 0 or width_cm <= 0 or quantity <= 0:
            raise ValueError(f"参数必须大于0，当前值: 长度={length_cm}, 宽度={width_cm}, 数量={quantity}")
        
        # 计算面积（用于显示）
        area_m2 = ((length_cm + 1) * (width_cm + 1) * quantity) / 10000
        
        # 计算原始价格（用于判断阶梯，不显示在输出中）
        original_price = area_m2 * prices['original_price']
        
        # 计算最终价格
        final_price = calculate_price(length_cm, width_cm, quantity, prices)
        
        # 构建结果字符串
        if is_diameter:
            import math
            diameter_cm = 2 * math.sqrt(length_cm * width_cm / math.pi)
            result_str = (
                f"直径: {round(diameter_cm, 2)}cm, "
                f"面积: {round(area_m2, 4)}㎡, "
                f"数量: {quantity}, "
                f"报价: {final_price}元"
            )
        else:
            result_str = (
                f"尺寸: {width_cm}*{length_cm}cm, "
                f"面积: {round(area_m2, 4)}㎡, "
                f"数量: {quantity}, "
                f"报价: {final_price}元"
            )
        
        # 返回结果
        return {
            'result': result_str
        }
        
    except Exception as e:
        # 错误情况也返回字符串
        return {
            'result': f"计算出错: {str(e)}"}

# 测试代码
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        "20*30 100个",                    # 标准格式
        "20x30 100",                      # 使用x分隔符
        "20X30",                          # 无数量
        "20×30 50个",                     # 使用×分隔符
        "长宽：20cm*30cm，数量50个",        # 自然语言格式
        "长宽：20cm",                      # 只有宽度
        "尺寸是20厘米乘以30厘米，要100个",   # 自然语言描述
        "20厘米宽，30厘米长，数量：5",       # 另一种自然语言格式
        "直径25cm，数量100",               # 直径格式
        "φ30cm 50个",                     # 直径符号格式
        "圆形直径20厘米，数量10",           # 直径自然语言
        "",                              # 空输入
        "10*10"                          # 小尺寸（保底价）
    ]
    
    for case in test_cases:
        result = main(case)
        print(f"输入: '{case}' => {result['result']}") 