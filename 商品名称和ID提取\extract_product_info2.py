import json

def extract_product_info(query_text: str) -> dict:
    """
    从查询文本中提取商品ID或商品名称
    :param query_text: 包含商品信息的查询文本
    :return: 包含提取结果的字典 {product_id, product_name}
    """
    try:
        result = {}
        
        # 从输入文本中提取商品ID
        if "商品ID：" in query_text:
            id_start = query_text.find("商品ID：") + len("商品ID：")
            id_end = query_text.find("，", id_start)
            if id_end == -1:  # 如果没有逗号，则取到字符串结尾
                id_end = len(query_text)
            product_id = query_text[id_start:id_end]
            result['product_id'] = product_id

        # 从输入文本中提取商品名
        if "商品名：" in query_text:
            name_start = query_text.find("商品名：") + len("商品名：")
            # 查找可能的结束标记
            end_markers = ["\n", "提问：", "问：", "A:", "答：", "#"]
            name_end = len(query_text)
            
            for marker in end_markers:
                pos = query_text.find(marker, name_start)
                if pos != -1 and pos < name_end:
                    name_end = pos
            
            product_name = query_text[name_start:name_end].strip()
            result['product_name'] = product_name
            
        # 处理"关于商品名称："格式
        elif "关于商品名称：" in query_text:
            name_start = query_text.find("关于商品名称：") + len("关于商品名称：")
            end_markers = ["\n", "提问：", "问：", "A:", "答：", "#"]
            name_end = len(query_text)
            
            for marker in end_markers:
                pos = query_text.find(marker, name_start)
                if pos != -1 and pos < name_end:
                    name_end = pos
            
            product_name = query_text[name_start:name_end].strip()
            result['product_name'] = product_name
            
        # 处理"平台商品名称"格式
        elif "平台商品名称" in query_text:
            patterns = ["平台商品名称\":\"", "平台商品名称\\\":\\\""]
            for pattern in patterns:
                if pattern in query_text:
                    # 找到第二个引号后的内容
                    first_part_end = query_text.find(";", query_text.find(pattern))
                    if first_part_end != -1:
                        second_part_start = first_part_end + 1
                        # 提取第二部分的引号内容
                        quote_start = query_text.find("\"", second_part_start)
                        if quote_start != -1:
                            quote_start += 1  # 跳过引号
                            quote_end = query_text.find("\"", quote_start)
                            if quote_end != -1:
                                product_name = query_text[quote_start:quote_end]
                                result['product_name'] = product_name
                                return result
        
        # 处理特殊格式："商品名\":\"" (修改这部分以正确处理转义字符)
        if "商品名" in query_text and "商品名：" not in query_text and "关于商品名称：" not in query_text:
            patterns = ["商品名\":\"", "商品名\\\":\\\""]
            for pattern in patterns:
                if pattern in query_text:
                    name_start = query_text.find(pattern) + len(pattern)
                    # 查找结束位置 (可能是 " 或 \")
                    for end_pattern in ["\"", "\\\"", "\";"]:
                        name_end = query_text.find(end_pattern, name_start)
                        if name_end != -1:
                            product_name = query_text[name_start:name_end]
                            result['product_name'] = product_name
                            return result  # 找到后立即返回

        return result
    except Exception as e:
        return {'error': f"提取商品信息失败: {str(e)}"}

# 修改main函数中处理列表类型content的部分
def main(input_data=None, content=None) -> dict:
    """
    主函数，处理输入并返回提取结果
    :param input_data: JSON格式的输入数据
    :param content: 直接提供的内容文本
    :return: 包含提取结果的字典
    """
    try:
        # 如果直接提供了content，优先使用
        if content:
            # 检查content是否已经是字典类型
            if isinstance(content, dict):
                content_data = content
            # 检查content是否已经是列表类型
            elif isinstance(content, list):
                # 直接处理列表中的每个项目
                for item in content:
                    if isinstance(item, dict) and 'content' in item:
                        result = extract_product_info(item['content'])
                        if 'product_name' in result:
                            return {'result': f"商品名：{result['product_name']}"}
                return {'result': "未找到商品名称"}
            else:
                # 尝试解析content为JSON
                try:
                    content_data = json.loads(content)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接使用content作为查询文本
                    query_text = content
                    result = extract_product_info(query_text)
                    if 'error' in result:
                        return {'result': result['error']}
                    
                    # 构建输出字符串
                    if 'product_name' in result:
                        return {'result': f"商品名：{result['product_name']}"}
                    elif 'product_id' in result:
                        return {'result': f"product_id：{result['product_id']}"}
                    else:
                        return {'result': "未找到商品信息"}
            
            # 处理JSON格式的content
            if isinstance(content_data, dict) and 'content' in content_data:
                if isinstance(content_data['content'], list):
                    # 从内容项中提取商品名
                    for item in content_data['content']:
                        if 'content' in item and item['content']:
                            result = extract_product_info(item['content'])
                            if 'product_name' in result:
                                return {'result': f"商品名：{result['product_name']}"}
                    return {'result': "未找到商品名称"}
                else:
                    query_text = str(content_data['content'])
            else:
                # 尝试直接从content中提取
                query_text = str(content)
        else:
            # 如果没有提供输入，使用默认测试数据
            if not input_data:
                input_data = '''{
                  "content": "咨询商品ID：488946234022，商品名：青岛啤酒鸿运当头355ml*12瓶装11度整箱送礼佳品"
                }'''
            
            # 检查input_data是否已经是字典类型
            if isinstance(input_data, dict):
                data = input_data
            # 检查input_data是否已经是列表类型
            elif isinstance(input_data, list):
                # 直接处理列表中的每个项目
                for item in input_data:
                    if isinstance(item, dict) and 'content' in item:
                        result = extract_product_info(item['content'])
                        if 'product_name' in result:
                            return {'result': f"商品名：{result['product_name']}"}
                return {'result': "未找到商品名称"}
            else:
                # 解析JSON输入
                try:
                    data = json.loads(input_data)
                except json.JSONDecodeError:
                    # 如果不是JSON，直接使用input_data作为查询文本
                    query_text = str(input_data)
                    result = extract_product_info(query_text)
                    if 'error' in result:
                        return {'result': result['error']}
                    
                    # 构建输出字符串
                    if 'product_name' in result:
                        return {'result': f"商品名：{result['product_name']}"}
                    elif 'product_id' in result:
                        return {'result': f"product_id：{result['product_id']}"}
                    else:
                        return {'result': "未找到商品信息"}
            
            # 检查是否有content字段，且是列表
            if isinstance(data, dict) and 'content' in data:
                if isinstance(data['content'], list):
                    # 从内容项中提取商品名
                    for item in data['content']:
                        if 'content' in item and item['content']:
                            result = extract_product_info(item['content'])
                            if 'product_name' in result:
                                return {'result': f"商品名：{result['product_name']}"}
                    return {'result': "未找到商品名称"}
                else:
                    query_text = str(data['content'])
            else:
                return {'result': "输入数据格式错误，缺少content字段"}
        
        # 提取商品信息
        result = extract_product_info(query_text)
        
        if 'error' in result:
            return {'result': result['error']}
        
        # 构建输出字符串 - 如果有商品名称，仅输出商品名
        if 'product_name' in result:
            output = f"商品名：{result['product_name']}"
        elif 'product_id' in result:
            output = f"product_id：{result['product_id']}"
        else:
            output = "未找到商品信息"
        
        return {'result': output}
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 程序入口点
if __name__ == "__main__":
    # 测试输入
    test_input = '''{
      "content": [
        {
          "metadata": {
            "_source": "knowledge",
            "dataset_id": "e5377dc6-fbc3-4b0b-8c65-e5a7299ac133",
            "dataset_name": "智阳商业——酒",
            "document_id": "9c063750-a742-4fed-ab90-37b5faf91868",
            "document_name": "海丁堡信阳毛尖.xlsx",
            "document_data_source_type": "upload_file",
            "segment_id": "767afd24-1cb4-40ae-ba82-2aa0a29afc6c",
            "retriever_from": "workflow",
            "score": 0.7056785314579846,
            "segment_hit_count": 3,
            "segment_word_count": 83,
            "segment_position": 4,
            "segment_index_node_hash": "d7681563e1c025c090d2efcf1857fc58129667daaaa32f4017a9117ddf5c9d23",
            "position": 1
          },
          "title": "海丁堡信阳毛尖.xlsx",
          "content": "商品名\\":\\"海丁堡信阳毛尖1L铝瓶\\";\\"问\\":\\"什么包装？原包装吗？\\";\\"答\\":\\"原厂包装1L×6瓶，由于快递防止破损，我们发货采用6瓶或2瓶拆箱用泡沫箱打包发货.\\""
        }
      ]
    }'''
    
    # 获取结果
    result = main(test_input)
    
    # 打印结果
    print(result['result'])