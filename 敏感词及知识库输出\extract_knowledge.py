import json
from typing import Union

def extract_knowledge_content(data: Union[dict, list]) -> dict:
    """
    从知识检索结果中提取content内容
    :param data: 包含知识检索结果的字典或列表
    :return: 包含提取结果的字典
    """
    try:
        # 如果输入是列表，直接使用
        if isinstance(data, list):
            content_list = data
        # 如果输入是字典，获取content字段
        else:
            content_list = data.get('content', [])
            
        if not content_list:
            return {'error': '没有找到content字段或content为空'}
            
        # 获取第一个结果的content
        if len(content_list) > 0:
            if isinstance(content_list[0], dict):
                content = content_list[0].get('content', '')
                if not content:
                    return {'error': '没有找到content内容'}
                return {'content': content}
            else:
                return {'error': 'content列表项格式不正确'}
        else:
            return {'error': 'content列表为空'}
        
    except Exception as e:
        return {'error': f"提取内容失败: {str(e)}"}

def main(input_data: Union[str, dict, list] = None, content: Union[str, dict, list] = None, conversation: dict = None, filters: dict = None) -> dict:
    """
    主函数，处理输入并返回提取结果
    :param input_data: JSON格式的字符串或Python对象（字典/列表）
    :param content: 直接提供的内容文本或Python对象
    :param conversation: 对话上下文信息
    :param filters: 过滤条件
    :return: 包含提取结果的字典
    """
    try:
        # 如果直接提供了content，优先使用
        if content is not None:
            # 如果content是字符串，尝试解析JSON
            if isinstance(content, str):
                data = json.loads(content)
            else:
                data = content
        else:
            # 如果没有提供输入，使用默认测试数据
            if input_data is None:
                input_data = '''{
                  "content": [
                    {
                      "content": "支付宝=\\"官方\\"\\n微信支付=\\"官方\\"\\n财付通=\\"官方\\"\\n京东支付=\\"官方\\"\\n抖店支付=\\"官方\\"\\n手机号=\\"\\"\\n微信号=\\"\\"\\n联系方式=\\"\\"\\nQQ=\\"\\"\\n微信=\\"\\"",
                      "title": "引导第三方敏感词替换.txt"
                    }
                  ]
                }'''
                data = json.loads(input_data)
            else:
                # 如果input_data是字符串，尝试解析JSON
                if isinstance(input_data, str):
                    data = json.loads(input_data)
                else:
                    data = input_data

        # 提取内容
        result = extract_knowledge_content(data)
        
        if 'error' in result:
            return {'result': result['error']}
            
        return {'result': result['content']}

    except json.JSONDecodeError:
        return {'result': "输入不是有效的JSON格式"}
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 程序入口点
if __name__ == "__main__":
    # 测试输入 - 列表形式
    test_input = [
        {
          "metadata": {
            "_source": "knowledge",
            "dataset_id": "03501c7b-36cf-4452-bc76-5e6f1a38bb53",
            "dataset_name": "通用知识库-曜银",
            "document_name": "引导第三方敏感词替换.txt"
          },
          "title": "引导第三方敏感词替换.txt",
          "content": "支付宝=\"官方\"\n微信支付=\"官方\"\n财付通=\"官方\"\n京东支付=\"官方\"\n抖店支付=\"官方\"\n手机号=\"\"\n微信号=\"\"\n联系方式=\"\"\nQQ=\"\"\n微信=\"\"" 
        }
    ]
    
    # 获取结果
    result = main(test_input)
    
    # 打印结果
    print(result['result']) 