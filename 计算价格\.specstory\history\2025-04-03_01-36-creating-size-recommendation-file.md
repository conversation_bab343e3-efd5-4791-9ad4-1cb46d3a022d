<!-- Generated by SpecStory -->

# Creating Size Recommendation File (2025-04-03 09:36:12)

_**User**_

quote_calculator.py 复制新增一个 尺码推荐的文件 重新定义一个名称

---

_**Assistant**_

我将帮你创建一个新的尺码推荐文件，基于原有的报价计算器代码结构。我们可以将其命名为 `size_recommender.py`。以下是新文件的代码：

```python:size_recommender.py
def recommend_size(product_type: str, width_cm: float = None, length_cm: float = None) -> dict:
    """
    根据产品类型和尺寸推荐标准尺码
    :param product_type: 产品类型（如：'名片'，'海报'，'展架'等）
    :param width_cm: 当前宽度(厘米)，可选
    :param length_cm: 当前长度(厘米)，可选
    :return: 推荐尺寸信息字典
    """
    # 标准尺寸对照表
    standard_sizes = {
        '名片': [
            {'width': 9, 'length': 5.4, 'description': '标准名片'},
            {'width': 8.5, 'length': 5.5, 'description': '欧标名片'}
        ],
        '海报': [
            {'width': 42, 'length': 29.7, 'description': 'A3'},
            {'width': 59.4, 'length': 42, 'description': 'A2'},
            {'width': 84.1, 'length': 59.4, 'description': 'A1'}
        ],
        '展架': [
            {'width': 60, 'length': 160, 'description': '标准易拉宝'},
            {'width': 80, 'length': 180, 'description': '大型展架'}
        ]
    }
    
    try:
        # 检查产品类型是否存在
        if product_type not in standard_sizes:
            return {
                'success': False,
                'message': f'未找到产品类型：{product_type}，支持的类型有：{", ".join(standard_sizes.keys())}'
            }
            
        # 获取该产品类型的所有标准尺寸
        available_sizes = standard_sizes[product_type]
        
        # 如果没有提供具体尺寸，返回所有标准尺寸
        if width_cm is None or length_cm is None:
            return {
                'success': True,
                'message': f'{product_type}的标准尺寸如下：',
                'sizes': available_sizes
            }
            
        # 如果提供了具体尺寸，找到最接近的标准尺寸
        closest_size = None
        min_difference = float('inf')
        
        for size in available_sizes:
            # 计算面积差异
            current_area = width_cm * length_cm
            standard_area = size['width'] * size['length']
            difference = abs(current_area - standard_area)
            
            if difference < min_difference:
                min_difference = difference
                closest_size = size
        
        return {
            'success': True,
            'message': f'根据您提供的尺寸（{width_cm}×{length_cm}cm），推荐使用以下标准尺寸：',
            'recommended_size': closest_size,
            'all_sizes': available_sizes
        }
        
    except Exception as e:
        return {
            'success': False,
            'message': f'推荐尺寸时出错：{str(e)}'
        }

def parse_input_string(input_str: str) -> dict:
    """
    从字符串输入中提取产品类型和尺寸，支持多种自然语言格式
    :param input_str: 输入字符串
    :return: 包含提取结果的字典 {product_type, width_cm, length_cm}
    """
    try:
        import re
        
        # 清理输入字符串
        input_str = input_str.lower().strip()
        
        # 提取产品类型
        product_types = ['名片', '海报', '展架']
        found_type = None
        for ptype in product_types:
            if ptype in input_str:
                found_type = ptype
                break
        
        # 提取尺寸（复用原有的尺寸解析逻辑）
        numbers = re.findall(r'\d+(?:\.\d+)?', input_str)
        width_cm = length_cm = None
        
        if len(numbers) >= 2:
            if '*' in input_str or 'x' in input_str or '×' in input_str:
                for separator in ['*', 'x', '×']:
                    if separator in input_str:
                        parts = input_str.split(separator, 1)
                        width_numbers = re.findall(r'\d+(?:\.\d+)?', parts[0])
                        length_numbers = re.findall(r'\d+(?:\.\d+)?', parts[1])
                        if width_numbers and length_numbers:
                            width_cm = float(width_numbers[-1])
                            length_cm = float(length_numbers[0])
                            break
            else:
                width_cm = float(numbers[0])
                length_cm = float(numbers[1])
        
        return {
            'product_type': found_type,
            'width_cm': width_cm,
            'length_cm': length_cm
        }
        
    except Exception as e:
        raise ValueError(f"解析输入失败: {str(e)}")

def main(content: str = "") -> dict:
    """
    尺码推荐的主函数
    :param content: 输入字符串，支持多种格式
    :return: 包含推荐结果的字典
    """
    try:
        if not content:
            return {'result': "请输入产品类型和尺寸（可选），例如：'名片 9*5.4' 或 '海报'"}
            
        # 解析输入
        params = parse_input_string(content)
        
        if not params['product_type']:
            return {'result': "请指定产品类型，支持：名片、海报、展架"}
            
        # 获取推荐结果
        result = recommend_size(
            params['product_type'],
            params['width_cm'],
            params['length_cm']
        )
        
        # 格式化输出
        if result['success']:
            if 'recommended_size' in result:
                recommended = result['recommended_size']
                output = (
                    f"{result['message']}\n"
                    f"推荐尺寸：{recommended['width']}×{recommended['length']}cm "
                    f"({recommended['description']})"
                )
            else:
                sizes_str = '\n'.join([
                    f"- {size['width']}×{size['length']}cm ({size['description']})"
                    for size in result['sizes']
                ])
                output = f"{result['message']}\n{sizes_str}"
        else:
            output = result['message']
            
        return {'result': output}
        
    except Exception as e:
        return {'result': f"处理出错: {str(e)}"}

# 测试代码
if __name__ == "__main__":
    # 测试用例
    test_cases = [
        "名片",                    # 只有产品类型
        "名片 9*5.4",             # 标准名片尺寸
        "海报 50x40",             # 非标准尺寸，需要推荐
        "展架 60*160",            # 标准展架尺寸
        "请帮我推荐一个海报尺寸",    # 自然语言查询
        "不存在的产品",            # 错误的产品类型
        ""                       # 空输入
    ]
    
    for case in test_cases:
        result = main(case)
        print(f"输入: '{case}' => {result['result']}")
```

这个新文件 `size_recommender.py` 保持了原有代码的结构风格，但功能完全不同。主要特点包括：

1. 提供标准尺寸推荐功能，支持名片、海报、展架等产品类型
2. 可以根据输入的具体尺寸推荐最接近的标准尺寸
3. 复用了原有的输入解析逻辑，但增加了产品类型的识别
4. 保持了相似的错误处理和返回格式
5. 包含了完整的测试用例

你可以通过传入产品类型（必须）和具体尺寸（可选）来获取尺寸推荐。如果只提供产品类型，系统会返回该产品的所有标准尺寸选项。

---



---

