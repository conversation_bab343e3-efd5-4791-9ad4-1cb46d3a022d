import json
from extract_dify_content import main

# 您提供的测试数据
test_data = {
  "arg1": [
    {
      "metadata": {
        "_source": "knowledge",
        "dataset_id": "b4d3141c-0694-4a27-bb04-d55f5f9c6020",
        "dataset_name": "商品知识库-曜银",
        "document_id": "7e626ded-f9a7-465b-bdbf-5e6c1b8b0c98",
        "document_name": "联想Thinkbook 14 Core5-220H 16G 1T 集显 14英寸 2.8K 120HZ 银灰色 2025款 (2).txt",
        "document_data_source_type": "upload_file",
        "segment_id": "74f31a0c-bcf4-4cf4-a73d-de13cb61ce91",
        "retriever_from": "workflow",
        "score": 0.6719476673639905,
        "segment_hit_count": 4,
        "segment_word_count": 167,
        "segment_position": 4,
        "segment_index_node_hash": "418f0813c1e1f3ac5adb40cf93cdb975f054e97e3872be915daa592e6b7b1ed9",
        "doc_metadata": {
          "source": "upload_file",
          "uploader": "jondfu",
          "upload_date": "2025-05-04 07:31:24",
          "document_name": "联想Thinkbook 14 Core5-220H 16G 1T 集显 14英寸 2.8K 120HZ 银灰色 2025款 (2).txt",
          "last_update_date": "2025-05-04 07:31:24"
        },
        "position": 1
      },
      "title": "联想Thinkbook 14 Core5-220H 16G 1T 集显 14英寸 2.8K 120HZ 银灰色 2025款 (2).txt",
      "content": "存储/硬盘\n商品名：联想ThinkBook 14 2025 Core 5 220H 16G 1T 2.8K 商务轻薄办公本\n容量：1TB\n类型：NVMe SSD M.2 PCIe 4.0\n读写：官方暂未提供相关信息，以实际读写速度为准\n扩展：双M.2 2280插槽，支持PCIe 4.0，预留1个空位，最大支持8TB（4TB+4TB）"
    },
    {
      "metadata": {
        "_source": "knowledge",
        "dataset_id": "b4d3141c-0694-4a27-bb04-d55f5f9c6020",
        "dataset_name": "商品知识库-曜银",
        "document_id": "7e626ded-f9a7-465b-bdbf-5e6c1b8b0c98",
        "document_name": "联想Thinkbook 14 Core5-220H 16G 1T 集显 14英寸 2.8K 120HZ 银灰色 2025款 (2).txt",
        "document_data_source_type": "upload_file",
        "segment_id": "22d9a779-b06c-492f-a2a5-dc1789661c61",
        "retriever_from": "workflow",
        "score": 0.6619880844469177,
        "segment_hit_count": 4,
        "segment_word_count": 120,
        "segment_position": 3,
        "segment_index_node_hash": "2969ee91528169045095ae722ee3b56de6f111304d17e83aafa8fce3ee831298",
        "doc_metadata": {
          "source": "upload_file",
          "uploader": "jondfu",
          "upload_date": "2025-05-04 07:31:24",
          "document_name": "联想Thinkbook 14 Core5-220H 16G 1T 集显 14英寸 2.8K 120HZ 银灰色 2025款 (2).txt",
          "last_update_date": "2025-05-04 07:31:24"
        },
        "position": 2
      },
      "title": "联想Thinkbook 14 Core5-220H 16G 1T 集显 14英寸 2.8K 120HZ 银灰色 2025款 (2).txt",
      "content": "内存\n商品名：联想ThinkBook 14 2025 Core 5 220H 16G 1T 2.8K 商务轻薄办公本\n容量：16GB\n类型：DDR5\n频率：5200MHz\n扩展：2个SO-DIMM插槽，最大支持64GB（32GB+32GB）"
    }
  ],
  "arg2": [
    {
      "metadata": {
        "_source": "knowledge",
        "dataset_id": "5405dcba-6eae-4886-bb28-5dbf88eb73b5",
        "dataset_name": "售后知识库-曜银",
        "document_id": "2fe98d33-1aca-4d04-9ad2-eff258c8baec",
        "document_name": "常见问题.xlsx",
        "document_data_source_type": "upload_file",
        "segment_id": "aad156ac-8257-460d-ba72-0e593d10895e",
        "retriever_from": "workflow",
        "score": 0.423104559141704,
        "segment_hit_count": 139,
        "segment_word_count": 98,
        "segment_position": 11,
        "segment_index_node_hash": "104e320bfd12d00d5888063923871d2bf11a52f8503b578fccc8dc393fe0c55c",
        "doc_metadata": {
          "source": "file_upload",
          "uploader": "jondfu",
          "upload_date": 1744792781,
          "document_name": "常见问题.xlsx",
          "last_update_date": 1744792781
        },
        "position": 1
      },
      "title": "常见问题.xlsx",
      "content": "问：硬盘实际容量不符\n答：这是由于硬盘厂商和操作系统对容量的计算方式不同（厂商用 1000 进制，系统用 1024 进制），加上系统预留和分区占用，导致实际可用空间略小，属于正常现象，并非产品问题"
    }
  ]
}

# 运行提取和格式化
result = main(arg1=test_data["arg1"], arg2=test_data["arg2"])
print(result["result"]) 